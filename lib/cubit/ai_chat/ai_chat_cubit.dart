import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:toii_social/core/repository/ai_chat_repository.dart';
import 'package:uuid/uuid.dart';

part 'ai_chat_state.dart';

class AiChatCubit extends Cubit<AiChatState> {
  final AiChatRepository _aiChatRepository;
  final _uuid = const Uuid();

  // Create users
  final types.User _user = const types.User(id: 'user', firstName: 'You');

  final types.User _assistant = const types.User(
    id: 'assistant',
    firstName: 'Gao',
  );

  AiChatCubit({required AiChatRepository aiChatRepository})
    : _aiChatRepository = aiChatRepository,
      super(const AiChatState());

  void addMessage(types.Message message) {
    final messages = [...state.messages];
    messages.insert(0, message);
    emit(state.copyWith(status: AiChatStatus.success, messages: messages));
  }

  Future<void> sendMessage(String text) async {
    // Add user message
    final userMessage = types.TextMessage(
      author: _user,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: _uuid.v4(),
      text: text,
    );

    addMessage(userMessage);

    // Set loading state
    emit(state.copyWith(status: AiChatStatus.loading));

    try {
      // Send message to AI
      final response = await _aiChatRepository.sendMessage(text);

      // Create AI response message
      final aiMessage = types.TextMessage(
        author: _assistant,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        id: _uuid.v4(),
        text: response.choices?.first.message?.content ?? '',
      );

      // Add AI response
      final messages = [...state.messages];
      messages.insert(0, aiMessage);
      emit(
        state.copyWith(
          status: AiChatStatus.success,
          messages: messages,
          errorMessage: null,
        ),
      );
    } catch (error) {
      // Handle error
      final errorMessage = types.TextMessage(
        author: _assistant,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        id: _uuid.v4(),
        text: 'Sorry, I encountered an error. Please try again.',
      );

      final messages = [...state.messages];
      messages.insert(0, errorMessage);
      emit(
        state.copyWith(
          status: AiChatStatus.failure,
          messages: messages,
          errorMessage: error.toString(),
        ),
      );
    }
  }

  types.User get user => _user;
  types.User get assistant => _assistant;
}
