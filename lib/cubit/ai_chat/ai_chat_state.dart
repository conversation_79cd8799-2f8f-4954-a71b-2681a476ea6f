part of 'ai_chat_cubit.dart';

enum AiChatStatus { initial, loading, success, failure }

class AiChatState extends Equatable {
  final AiChatStatus status;
  final List<types.Message> messages;
  final String? errorMessage;

  const AiChatState({
    this.status = AiChatStatus.initial,
    this.messages = const [],
    this.errorMessage,
  });

  AiChatState copyWith({
    AiChatStatus? status,
    List<types.Message>? messages,
    String? errorMessage,
  }) {
    return AiChatState(
      status: status ?? this.status,
      messages: messages ?? this.messages,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, messages, errorMessage];
}
