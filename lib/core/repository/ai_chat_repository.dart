import 'package:toii_social/core/service/ai_chat_service.dart';
import 'package:toii_social/model/ai_chat/ai_chat_request_model.dart';
import 'package:toii_social/model/ai_chat/ai_chat_response_model.dart';

abstract class AiChatRepository {
  Future<AiChatResponseModel> sendMessage(String userMessage);
}

class AiChatRepositoryImpl implements AiChatRepository {
  final AiChatService _aiChatService;
  static const String _bearerToken = 'Bearer rpa_DQSHSPXPOEVGF9HWMBMUFT59O9ORMERVM2QQE36C1mz6bk';
  static const String _systemMessage = 'You are <PERSON>, a friendly, creative, and witty AI assistant for GAO social Media.';

  AiChatRepositoryImpl({required AiChatService aiChatService})
      : _aiChatService = aiChatService;

  @override
  Future<AiChatResponseModel> sendMessage(String userMessage) async {
    final request = AiChatRequestModel(
      messages: [
        const AiChatMessageModel(
          role: 'system',
          content: _systemMessage,
        ),
        AiChatMessageModel(
          role: 'user',
          content: userMessage,
        ),
      ],
      includeReasoning: false,
      stream: false,
    );

    return await _aiChatService.sendMessage(request, _bearerToken);
  }
}
